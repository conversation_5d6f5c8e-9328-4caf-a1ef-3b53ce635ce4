import sys
import os

# 获取项目根目录的绝对路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)  # 将项目根目录添加到模块搜索路径的最前面

from db.database import init_db, add_api_key

def main():
    print("API Key 添加工具")
    print("-" * 30)
    
    while True:
        key = input("请输入要添加的 API Key (输入 'q' 退出): ").strip()
        
        if key.lower() == 'q':
            break
            
        if not key:
            print("API Key 不能为空！")
            continue
            
        try:
            init_db()
            add_api_key(key)
            print(f"成功添加 API Key: {key}")
        except Exception as e:
            print(f"添加失败: {str(e)}")

if __name__ == '__main__':
    main()