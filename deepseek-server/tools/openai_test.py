from openai import OpenAI

client = OpenAI(api_key="truetokentest", base_url="http://localhost:8081/v1")

messages = [{"role": "user", "content": """你好"""}]
response = client.chat.completions.create(
    model="deepseek",
    messages=messages,
    stream=True,
    temperature=0.9
)


#v1
content = ""

for chunk in response:
    content += chunk.choices[0].delta.content
    print(content)
print(response.choices[0].message.content)

#v2
#print(response.choices[0].message.reasoning_content)
#print(response.choices[0].message.content)


# content = ""
# for chunk in response:
#     content += chunk.choices[0].delta.content
#     if content:
#         print(content)
#     else:
#         print(chunk.choices[0].delta.reasoning_content)