document.addEventListener('DOMContentLoaded', function() {
    // 初始加载数据
    fetchUsageData();
    
    // 设置刷新按钮事件
    document.getElementById('refreshBtn').addEventListener('click', fetchUsageData);
    
    // 获取使用量数据的函数
    function fetchUsageData() {
        fetch('/usage')
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                updateUsageTable(data);
                updateLastUpdated();
            })
            .catch(error => {
                console.error('获取数据时出错:', error);
                alert('获取数据失败，请稍后再试');
            });
    }
    
    // 更新表格数据
    function updateUsageTable(data) {
        const tableBody = document.getElementById('usageData');
        tableBody.innerHTML = '';
        
        // 将对象转换为数组并按使用量排序
        const sortedData = Object.entries(data)
            .sort((a, b) => b[1] - a[1]); // 按 token 使用量降序排序
        
        if (sortedData.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="2" style="text-align: center;">暂无数据</td>';
            tableBody.appendChild(row);
            return;
        }
        
        sortedData.forEach(([key, usage]) => {
            const row = document.createElement('tr');
            
            // 创建 API Key 单元格
            const keyCell = document.createElement('td');
            // 显示部分 API Key，保护隐私
            const maskedKey = maskApiKey(key);
            keyCell.textContent = maskedKey;
            
            // 创建使用量单元格
            const usageCell = document.createElement('td');
            usageCell.textContent = formatNumber(usage);
            
            // 添加到行
            row.appendChild(keyCell);
            row.appendChild(usageCell);
            
            // 添加到表格
            tableBody.appendChild(row);
        });
    }
    
    // 更新最后更新时间
    function updateLastUpdated() {
        const now = new Date();
        const formattedTime = now.toLocaleTimeString('zh-CN');
        document.getElementById('lastUpdated').textContent = `最后更新: ${formattedTime}`;
    }
    
    // 格式化数字，添加千位分隔符
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
    
    // 掩盖 API Key，只显示前4位和后4位
    function maskApiKey(key) {
        if (key.length <= 8) return key;
        return key.substring(0, 4) + '...' + key.substring(key.length - 4);
    }
});