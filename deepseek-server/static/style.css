* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

body {
    background-color: #f5f7fb;
    color: #1a1f36;
    line-height: 1.6;
    padding: 2rem;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
}

h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: #1a1f36;
    font-size: 2rem;
    font-weight: 600;
}

.card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    padding: 2rem;
    transition: transform 0.3s ease;
}

.refresh-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eef0f5;
}

.button-group {
    display: flex;
    gap: 10px;
}

.button-group button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.btn-primary, .btn-success {
    color: white;
    padding: 0.75rem 1.5rem;
}

.btn-primary {
    background-color: #4f46e5;
}

.btn-primary:hover {
    background-color: #4338ca;
    transform: translateY(-1px);
}

.btn-success {
    background-color: #10b981;
}

.btn-success:hover {
    background-color: #059669;
    transform: translateY(-1px);
}

#lastUpdated {
    color: #6b7280;
    font-size: 0.875rem;
}

.table-container {
    overflow-x: auto;
    border-radius: 8px;
}

table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

th, td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eef0f5;
}

th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #4b5563;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

tr:last-child td {
    border-bottom: none;
}

tr:hover {
    background-color: #f9fafb;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    border-radius: 16px;
    padding: 2rem;
    width: 400px;
    position: relative;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    margin: 0 auto;
}

.modal-content h3 {
    margin-bottom: 1.5rem;
    color: #1a1f36;
    font-weight: 600;
}

.close {
    position: absolute;
    right: 1.5rem;
    top: 1.5rem;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: color 0.2s ease;
    line-height: 1;
}

.close:hover {
    color: #1a1f36;
}

#newApiKey {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    margin: 1rem 0;
    transition: border-color 0.2s ease;
}

#newApiKey:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.modal-content button {
    width: 100%;
    padding: 0.75rem;
    background-color: #4f46e5;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-content button:hover {
    background-color: #4338ca;
}

.message-success {
    color: #059669;
    background-color: #ecfdf5;
    padding: 0.75rem;
    border-radius: 6px;
    margin-top: 1rem;
    font-size: 0.875rem;
}

.message-error {
    color: #dc2626;
    background-color: #fef2f2;
    padding: 0.75rem;
    border-radius: 6px;
    margin-top: 1rem;
    font-size: 0.875rem;
}

.input-error {
    border-color: #dc2626 !important;
    animation: shake 0.5s;
}

.loading-indicator {
    border: 2px solid #e5e7eb;
    border-top: 2px solid #4f46e5;
    width: 1.25rem;
    height: 1.25rem;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-4px); }
    75% { transform: translateX(4px); }
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    body {
        padding: 1rem;
    }

    .card {
        padding: 1rem;
    }

    .button-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-content {
        width: 90%;
        margin: 10% auto;
    }
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.header h1 {
    margin: 0;
}

.btn-danger {
    background-color: #dc2626;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-danger:hover {
    background-color: #b91c1c;
    transform: translateY(-1px);
}

.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f7fb;
}

.login-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    width: 100%;
    max-width: 400px;
}

.login-card h1 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

.input-group {
    margin-bottom: 1rem;
}

.input-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.login-card button {
    width: 100%;
    margin-top: 1rem;
}

/* 添加到文件末尾 */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

td .btn-danger {
    background-color: #dc2626;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

td .btn-danger:hover {
    background-color: #b91c1c;
}

