<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek API 使用统计</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DeepSeek API 使用统计</h1>
            <button id="logoutBtn" class="btn-danger" onclick="handleLogout()">注销</button>
        </div>
        <div class="card">
            <div class="refresh-container">
                <div class="button-group">
                    <button id="refreshBtn" class="btn-primary" onclick="fetchUsageData()">刷新数据</button>
                    <button id="addKeyBtn" class="btn-success" onclick="openAddKeyModal()">添加 Key</button>
                </div>
                <span id="lastUpdated"></span>
            </div>
            <div class="table-container">
                <table id="usageTable">
                    <thead>
                        <tr>
                            <th>API Key</th>
                            <th>Token 使用量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="usageData">
                        <!-- 数据将通过 JavaScript 动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 添加 Key 的模态框 -->
    <div id="addKeyModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeAddKeyModal()">&times;</span>
            <h3>添加 API Key</h3>
            <input type="text" id="newApiKey" placeholder="请输入 API Key">
            <button onclick="submitNewKey()">提交</button>
            <div id="modalMessage"></div>
        </div>
    </div>

    <script>
        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', function() {
            fetchUsageData();
        });
        
        // 获取使用量数据的函数
        function fetchUsageData() {
            console.log('正在获取数据...');
            fetch('/usage')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应不正常');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('获取到数据:', data);
                    updateUsageTable(data);
                    updateLastUpdated();
                })
                .catch(error => {
                    console.error('获取数据时出错:', error);
                    alert('获取数据失败，请稍后再试');
                });
        }
        
        // 更新表格数据
        // 替换现有的 updateUsageTable 函数
        function updateUsageTable(data) {
            const tableBody = document.getElementById('usageData');
            tableBody.innerHTML = '';
            
            const sortedData = Object.entries(data)
                .sort((a, b) => b[1] - a[1]);
        
            if (sortedData.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = '<td colspan="3" style="text-align: center;">暂无数据</td>';
                tableBody.appendChild(row);
                return;
            }
            
            sortedData.forEach(([key, usage]) => {
                const row = document.createElement('tr');
                
                // API Key 单元格
                const keyCell = document.createElement('td');
                const maskedKey = maskApiKey(key);
                keyCell.textContent = maskedKey;
                
                // 使用量单元格
                const usageCell = document.createElement('td');
                usageCell.textContent = formatNumber(usage);
                
                // 操作单元格
                const actionCell = document.createElement('td');
                const deleteBtn = document.createElement('button');
                deleteBtn.className = 'btn-danger btn-sm';
                deleteBtn.textContent = '删除';
                deleteBtn.onclick = () => confirmDeleteKey(key);
                actionCell.appendChild(deleteBtn);
                
                row.appendChild(keyCell);
                row.appendChild(usageCell);
                row.appendChild(actionCell);
                tableBody.appendChild(row);
            });
        }

        // 添加删除相关的函数
        function confirmDeleteKey(key) {
            if (confirm('确定要删除这个 API Key 吗？此操作不可恢复。')) {
                deleteKey(key);
            }
        }

        async function deleteKey(key) {
            try {
                const response = await fetch('/delete-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ key: key })
                });

                const result = await response.json();
                if (response.ok) {
                    alert('删除成功');
                    fetchUsageData();
                } else {
                    throw new Error(result.error || '未知错误');
                }
            } catch (error) {
                alert(`删除失败: ${error.message}`);
            }
        }
        
        // 更新最后更新时间
        function updateLastUpdated() {
            const now = new Date();
            const formattedTime = now.toLocaleTimeString('zh-CN');
            document.getElementById('lastUpdated').textContent = `最后更新: ${formattedTime}`;
        }
        
        // 格式化数字，添加千位分隔符
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }
        
        // 掩盖 API Key，只显示前4位和后4位
        function maskApiKey(key) {
            if (key.length <= 8) return key;
            return key.substring(0, 4) + '...' + key.substring(key.length - 4);
        }

        // 新增的添加 Key 相关函数
        function openAddKeyModal() {
            document.getElementById('newApiKey').value = '';
            document.getElementById('modalMessage').textContent = '';
            document.getElementById('addKeyModal').style.display = 'block';
        }

        function closeAddKeyModal() {
            document.getElementById('addKeyModal').style.display = 'none';
        }

        async function submitNewKey() {
            const key = document.getElementById('newApiKey').value.trim();
            if (!key) {
                showModalMessage('API Key 不能为空', 'error');
                return;
            }

            try {
                const response = await fetch('/add-key', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ key: key })
                });

                const result = await response.json();
                if (response.ok) {
                    showModalMessage('添加成功，2秒后刷新...', 'success');
                    setTimeout(() => {
                        closeAddKeyModal();
                        fetchUsageData();
                    }, 2000);
                } else {
                    throw new Error(result.error || '未知错误');
                }
            } catch (error) {
                showModalMessage(`添加失败: ${error.message}`, 'error');
            }
        }

        function showModalMessage(message, type) {
            const messageDiv = document.getElementById('modalMessage');
            messageDiv.textContent = message;
            messageDiv.className = `message-${type}`;
        }
        
        function handleLogout() {
            window.location.href = '/logout';
        }
    </script>
</body>
</html>