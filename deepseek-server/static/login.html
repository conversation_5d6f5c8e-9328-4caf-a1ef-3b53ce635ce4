<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSeek API 管理系统 - 登录</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <h1>DeepSeek API 管理系统</h1>
            <form id="loginForm" onsubmit="return handleLogin(event)">
                <div class="input-group">
                    <input type="text" id="username" placeholder="用户名" required>
                </div>
                <div class="input-group">
                    <input type="password" id="password" placeholder="密码" required>
                </div>
                <button type="submit" class="btn-primary">登录</button>
                <div id="loginMessage" class="message"></div>
            </form>
        </div>
    </div>

    <script>
        async function handleLogin(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });

                const result = await response.json();
                if (response.ok) {
                    window.location.href = '/usage-dashboard';
                } else {
                    showMessage(result.error, 'error');
                }
            } catch (error) {
                showMessage('登录失败，请稍后重试', 'error');
            }
        }

        function showMessage(message, type) {
            const messageDiv = document.getElementById('loginMessage');
            messageDiv.textContent = message;
            messageDiv.className = `message message-${type}`;
        }
    </script>
</body>
</html>