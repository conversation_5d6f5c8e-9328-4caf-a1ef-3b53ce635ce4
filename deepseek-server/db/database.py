import sqlite3
from contextlib import contextmanager
from configs.config import <PERSON><PERSON><PERSON><PERSON><PERSON>

@contextmanager
def get_db():
    conn = sqlite3.connect(DATABASE)
    try:
        yield conn
    finally:
        conn.close()

def init_db():
    with get_db() as conn:
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS api_keys (
                key TEXT PRIMARY KEY,
                total_tokens INTEGER DEFAULT 0,
                is_valid BOOLEAN DEFAULT 1
            )
        ''')
        # 插入预设的API keys
        initial_keys = [
            ('yourkey', 0, 1),
            ('yourkey1', 0, 1),
            ('yourkey2', 0, 1)
        ]
        cursor.executemany('INSERT OR IGNORE INTO api_keys (key, total_tokens, is_valid) VALUES (?, ?, ?)',
                          initial_keys)
        conn.commit()

def validate_key(api_key):
    with get_db() as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT is_valid FROM api_keys WHERE key = ?', (api_key,))
        result = cursor.fetchone()
        return bool(result and result[0])

def update_token_usage(api_key, total_usage):
    if total_usage > 0:
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE api_keys 
                SET total_tokens = total_tokens + ? 
                WHERE key = ?
            ''', (total_usage, api_key))
            conn.commit()

def get_all_usage():
    with get_db() as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT key, total_tokens FROM api_keys')
        return {row[0]: row[1] for row in cursor.fetchall()}

def add_api_key(api_key):
    with get_db() as conn:
        cursor = conn.cursor()
        try:
            # 修正SQL语句，确保参数数量与占位符一致
            cursor.execute('INSERT INTO api_keys (key, total_tokens, is_valid) VALUES (?, 0, 1)', (api_key,))
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e

def delete_api_key(api_key):
    with get_db() as conn:
        cursor = conn.cursor()
        try:
            cursor.execute('DELETE FROM api_keys WHERE key = ?', (api_key,))
            conn.commit()
        except Exception as e:
            conn.rollback()
            raise e