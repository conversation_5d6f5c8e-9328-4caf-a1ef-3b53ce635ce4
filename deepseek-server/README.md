# DeepSeek 服务器

## 项目简介

DeepSeek 服务器是一个 API 代理服务，用于转发和管理对 DeepSeek AI 模型的请求。该服务提供了 API 密钥验证、使用量统计和流式响应处理等功能。

## 功能特点

- API 密钥验证和管理
- 请求转发到 DeepSeek AI 模型
- 流式响应处理
- Token 使用量统计
- 支持 `<think>` 标签的思考模式处理

## 安装与配置

### 环境要求

- Python 3.7+
- Flask
- SQLite3

### 安装步骤

1. 克隆仓库到本地：
```bash
git clone <仓库地址>
cd deepseek-server
 ```



2. 配置服务：
编辑 configs/config.py 文件，设置目标 API 地址、数据库路径和服务器端口等参数。

## 使用方法
### 启动服务器
```bash
python key-server.py
 ```

服务器将在配置的端口上启动（默认为 5000）。

### API 端点
- /v1/chat/completions - 标准聊天完成 API
- /v2/chat/completions - 支持思考模式的聊天完成 API
- /usage - 查看 API 密钥使用情况
### 添加 API 密钥
使用提供的工具添加新的 API 密钥：

```bash
python tools/add_key.py
 ```

## 项目结构
```plaintext
deepseek-server/
├── configs/           # 配置文件
├── db/                # 数据库相关代码
├── routes/            # API 路由定义
├── tools/             # 实用工具
├── key-server.py      # 主服务器文件
└── README.md          # 项目说明文档
 ```

## 开发说明
### 数据库结构
项目使用 SQLite 数据库存储 API 密钥和使用统计信息。数据库表结构如下：

- api_keys 表：存储 API 密钥、使用量和有效状态
### 批量更新机制
为了减少数据库访问频率，系统采用批量更新机制，每累积一定数量的 token 使用量后才更新数据库。

## 许可证
[在此添加许可证信息]

## 联系方式
[在此添加联系方式]