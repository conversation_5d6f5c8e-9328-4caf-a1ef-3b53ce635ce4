from flask import request, Response, jsonify, session, redirect, url_for
import requests
import json
from configs.config import TARGET_API
import sqlite3
from db.database import validate_key, update_token_usage, get_all_usage, add_api_key, delete_api_key
from functools import wraps
from concurrent.futures import ThreadPoolExecutor

# Create a thread pool with a maximum of 10 workers (adjust based on your needs)
executor = ThreadPoolExecutor(max_workers=50)

# Login required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('logged_in'):
            return redirect(url_for('login_page'))
        return f(*args, **kwargs)
    return decorated_function

def setup_routes(app):
    # Set session secret key
    app.secret_key = 'your-secret-key-here'  # Replace with a secure key

    @app.route('/v1/chat/completions', methods=['POST'])
    def proxy():
        # Validate API key
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({"error": "Missing Authorization header"}), 401

        api_key = auth_header.split(' ')[1]
        if not validate_key(api_key):
            return jsonify({"error": "Invalid API key"}), 401

        # Get client request data
        req_data = request.get_json()
        stream = req_data.get('stream', False)

        # Forward request to target API
        try:
            target_response = requests.post(
                TARGET_API,
                headers={"Content-Type": "application/json"},
                json=req_data,
                stream=stream
            )
        except Exception as e:
            return jsonify({"error": str(e)}), 500

        if stream:
            # Streaming response handling
            def generate():
                total_usage = 0
                batch_size = 10
                for chunk in target_response.iter_lines():
                    if chunk:
                        try:
                            decoded = chunk.decode().lstrip("data: ").strip()
                            if decoded == "[DONE]":
                                yield f"data: [DONE]\n\n"
                                break

                            data = json.loads(decoded)
                            total_usage += 1
                            finish = data["choices"][0].get("finish_reason")

                            if total_usage % batch_size == 0:
                                # Asynchronously update token usage
                                executor.submit(update_token_usage, api_key, batch_size)
                            if finish == "stop" and "usage" in data:
                                # Asynchronously update final token usage
                                executor.submit(update_token_usage, api_key, data["usage"]["prompt_tokens"])

                            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                        except Exception as e:
                            app.logger.error(f"Error processing chunk: {str(e)}")

            return Response(generate(), mimetype='text/event-stream')
        else:
            # Non-streaming response handling
            try:
                response_data = target_response.json()
                if "usage" in response_data:
                    total_tokens = response_data["usage"].get("total_tokens", 0)
                    # Asynchronously update token usage
                    executor.submit(update_token_usage, api_key, total_tokens)
                json_response = json.dumps(response_data, ensure_ascii=False)
                return Response(json_response, mimetype='application/json')
            except Exception as e:
                app.logger.error(f"Error processing non-stream response: {str(e)}")
                return jsonify({"error": "Internal server error"}), 500

    @app.route('/v2/chat/completions', methods=['POST'])
    def proxy_v2():
        # Validate API key
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({"error": "Missing Authorization header"}), 401

        api_key = auth_header.split(' ')[1]
        if not validate_key(api_key):
            return jsonify({"error": "Invalid API key"}), 401

        # Get client request data
        req_data = request.get_json()
        stream = req_data.get('stream', False)

        # Forward request to target API
        try:
            target_response = requests.post(
                TARGET_API,
                headers={"Content-Type": "application/json"},
                json=req_data,
                stream=stream
            )
        except Exception as e:
            return jsonify({"error": str(e)}), 500

        if stream:
            # Streaming response handling
            def generate():
                think_mode = True
                current_content_chunk = ""
                current_reasoning_content_chunk = ""
                total_usage = 0
                batch_size = 10
                first_chunk_processed = False
                first_content_chunk = True

                for chunk in target_response.iter_lines():
                    if chunk:
                        try:
                            decoded = chunk.decode().lstrip("data: ").strip()
                            if decoded == "[DONE]":
                                yield f"data: [DONE]\n\n"
                                break

                            data = json.loads(decoded)
                            total_usage += 1

                            # Ensure reasoning_content field exists in delta
                            if 'choices' in data and data['choices'] and 'delta' in data['choices'][0]:
                                data['choices'][0]['delta']['reasoning_content'] = ""

                            text_chunk = data['choices'][0]['delta'].get('content', '')
                            finish = data["choices"][0].get("finish_reason")

                            process_text = text_chunk
                            if not first_chunk_processed:
                                first_chunk_processed = True
                                think_mode = True

                            while process_text:
                                think_end_index = process_text.find("</think>")

                                if think_end_index != -1 and think_mode:
                                    reasoning_content_part = process_text[:think_end_index + len("</think>")]
                                    current_reasoning_content_chunk += reasoning_content_part
                                    data['choices'][0]['delta']['reasoning_content'] = current_reasoning_content_chunk

                                    content_part_after_think = process_text[think_end_index + len("</think>"):]
                                    current_content_chunk += content_part_after_think
                                    data['choices'][0]['delta']['content'] = current_content_chunk

                                    process_text = ""
                                    think_mode = False
                                else:
                                    if think_mode:
                                        current_reasoning_content_chunk += process_text
                                        data['choices'][0]['delta']['reasoning_content'] = current_reasoning_content_chunk
                                    else:
                                        content_to_add = process_text
                                        if first_content_chunk:
                                            if content_to_add.startswith("\n\n"):
                                                content_to_add = content_to_add[2:]
                                            first_content_chunk = False
                                        current_content_chunk += content_to_add
                                        data['choices'][0]['delta']['content'] = current_content_chunk
                                    process_text = ""
                            data['choices'][0]['delta']['content'] = current_content_chunk
                            data['choices'][0]['delta']['reasoning_content'] = current_reasoning_content_chunk

                            if total_usage % batch_size == 0:
                                # Asynchronously update token usage
                                executor.submit(update_token_usage, api_key, batch_size)
                            if finish == "stop" and "usage" in data:
                                # Asynchronously update final token usage
                                executor.submit(update_token_usage, api_key, data["usage"]["prompt_tokens"])

                            yield f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
                            current_content_chunk = ""
                            current_reasoning_content_chunk = ""
                        except Exception as e:
                            app.logger.error(f"Error processing chunk: {str(e)}")

            return Response(generate(), mimetype='text/event-stream')
        else:
            # Non-streaming response handling
            try:
                response_data = target_response.json()
                full_content = response_data['choices'][0]['message']['content']
                reasoning_content = ""
                content = ""

                # Process <think> tags
                think_end = full_content.find("</think>")
                if think_end != -1:
                    reasoning_content = full_content[:think_end + len("</think>")]
                    content = full_content[think_end + len("</think>"):]
                else:
                    reasoning_content = ""
                    content = full_content

                # Update response data
                response_data['choices'][0]['message']['content'] = content.strip()
                response_data['choices'][0]['message']['reasoning_content'] = reasoning_content

                # Asynchronously update token usage
                if "usage" in response_data:
                    total_tokens = response_data["usage"].get("total_tokens", 0)
                    executor.submit(update_token_usage, api_key, total_tokens)

                json_response = json.dumps(response_data, ensure_ascii=False)
                return Response(json_response, mimetype='application/json')
            except Exception as e:
                app.logger.error(f"Error processing non-stream response: {str(e)}")
                return jsonify({"error": "Internal server error"}), 500

    @app.route('/')
    def login_page():
        if session.get('logged_in'):
            return redirect(url_for('usage_dashboard'))
        return app.send_static_file('login.html')

    @app.route('/login', methods=['POST'])
    def login():
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        # Replace with your actual authentication logic
        if username == "admin" and password == "123":
            session['logged_in'] = True
            return jsonify({"message": "Login successful"})
        return jsonify({"error": "Invalid username or password"}), 401

    @app.route('/logout')
    def logout():
        session.clear()
        return redirect(url_for('login_page'))

    @app.route('/usage', methods=['GET'])
    @login_required
    def get_usage():
        return jsonify(get_all_usage())

    @app.route('/usage-dashboard')
    @login_required
    def usage_dashboard():
        return app.send_static_file('usage.html')

    @app.route('/add-key', methods=['POST'])
    @login_required
    def web_add_key():
        try:
            data = request.get_json()
            api_key = data.get('key')
            if not api_key:
                return jsonify({"error": "Missing API key"}), 400
            
            # Validate key length (8-64 characters)
            if len(api_key) < 8 or len(api_key) > 64:
                return jsonify({"error": "API key length must be between 8 and 64 characters"}), 400

            # Validate key format (alphanumeric only)
            if not api_key.isalnum():
                return jsonify({"error": "API key must contain only letters and numbers"}), 400

            add_api_key(api_key)
            return jsonify({"message": "API key added successfully"}), 201
            
        except sqlite3.IntegrityError:
            return jsonify({"error": "API key already exists"}), 409
        except Exception as e:
            app.logger.error(f"Failed to add key: {str(e)}")
            return jsonify({"error": "Internal server error"}), 500

    @app.route('/delete-key', methods=['POST'])
    @login_required
    def delete_key():
        try:
            data = request.get_json()
            api_key = data.get('key')
            if not api_key:
                return jsonify({"error": "Missing API key"}), 400
            
            delete_api_key(api_key)
            return jsonify({"message": "API key deleted successfully"}), 200
            
        except Exception as e:
            app.logger.error(f"Failed to delete key: {str(e)}")
            return jsonify({"error": "Internal server error"}), 500