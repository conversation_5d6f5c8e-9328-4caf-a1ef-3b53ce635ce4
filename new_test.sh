# curl -H "Accept: application/json" \
#      -H "Content-type: application/json" \
#      -H "Authorization: <PERSON><PERSON> falsetokentest" \
#      -X POST -d '{
#         "model": "deepseek",
#         "messages": [{"role": "user", "content": "你好"}],
#         "max_tokens": 2046,
#         "seed": null,
#         "temperature": 0.6,
#         "top_p": 0.7,
#         "top_k": 50,
#         "stream": true
#       }' http://localhost:8085/v1/chat/completions

# curl -H "Accept: application/json" \
#      -H "Content-type: application/json" \
#      -H "Authorization: Bearer falsetokentest" \
#      -X POST -d '{
#         "model": "xiaoyu",
#         "messages": [{"role": "user", "content": "翻译为中文\n\nမြန်မာအမျိုးသမီးစုံတွဲများကို ညစ်ညမ်းဗီဒီယိုရိုက်ကူးပြီး Live လွှင့်ကာ ရရှိသည့်အကျိုးအမြတ်များကို ခေါင်းပုံဖြတ်ရယူခဲ့သည့် တရုတ်နိုင်ငံသားများနှင့် မြန်မာအမျိုးသမီးတစ်ဦးကို ထောင်ဒဏ်များချမှတ်ခဲ့ကြောင်း ပြန်ကြားရေးဝန်ကြီးဌာနက ထုတ်ပြန်သည်။"}],
#         "stream": true
#       }' http://localhost:8085/v2/chat/completions


curl -H "Accept: application/json" \
     -H "Content-type: application/json" \
     -H "Authorization: Bearer falsetokentest" \
     -X POST -d '{
        "model": "xiaoyu",
        "messages": [{"role": "user", "content": "你好"}],
        "stream": true
      }' http://localhost:8085/v2/chat/completions